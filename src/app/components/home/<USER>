<div class="container">
  <div class="hero-section fade-in">
    <div class="hero-content">
      <h1 class="hero-title">
        <i class="fas fa-brain"></i>
        Інтерактивна система тестування
      </h1>
      <p class="hero-description">
        Перевірте свої знання з різних предметів. Гнучкі налаштування дозволяють створити тест саме під ваші потреби.
      </p>
      <button class="btn btn-primary btn-large" (click)="startTest()">
        <i class="fas fa-play"></i>
        Почати тестування
      </button>
    </div>
  </div>

  <div class="stats-section">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-book"></i>
        </div>
        <div class="stat-content">
          <h3>{{ availableSubjects.length }}</h3>
          <p>Предметів</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-question-circle"></i>
        </div>
        <div class="stat-content">
          <h3>{{ totalQuestions }}</h3>
          <p>Питань</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-cogs"></i>
        </div>
        <div class="stat-content">
          <h3>Гнучкі</h3>
          <p>Налаштування</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="stat-content">
          <h3>Детальна</h3>
          <p>Статистика</p>
        </div>
      </div>
    </div>
  </div>

  <div class="subjects-section">
    <h2 class="section-title">
      <i class="fas fa-list"></i>
      Доступні предмети
    </h2>
    
    <div class="subjects-grid">
      <div class="subject-card" *ngFor="let subject of availableSubjects">
        <div class="subject-header">
          <h3>{{ subject }}</h3>
          <span class="question-count">
            {{ getSubjectQuestionCount(subject) }} питань
          </span>
        </div>
        <div class="subject-description">
          <p>Тестування знань з предмету "{{ subject }}"</p>
        </div>
      </div>
    </div>
  </div>

  <div class="features-section">
    <h2 class="section-title">
      <i class="fas fa-star"></i>
      Особливості системи
    </h2>
    
    <div class="features-grid">
      <div class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-random"></i>
        </div>
        <h3>Перемішування</h3>
        <p>Питання та варіанти відповідей можуть бути перемішані для кращого тестування</p>
      </div>
      
      <div class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-sliders-h"></i>
        </div>
        <h3>Налаштування</h3>
        <p>Виберіть предмети та кількість питань з кожного для персоналізованого тесту</p>
      </div>
      
      <div class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-clock"></i>
        </div>
        <h3>Відстеження часу</h3>
        <p>Система відстежує час проходження тесту та показує детальну статистику</p>
      </div>
      
      <div class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-chart-pie"></i>
        </div>
        <h3>Аналітика</h3>
        <p>Детальні результати з розбивкою по предметах та окремих питаннях</p>
      </div>
    </div>
  </div>
</div>
