import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TestService } from '../../services/test.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {
  availableSubjects: string[] = [];
  totalQuestions: number = 0;

  constructor(
    private router: Router,
    private testService: TestService
  ) { }

  ngOnInit(): void {
    this.availableSubjects = this.testService.getAvailableSubjects();
    this.totalQuestions = this.availableSubjects.reduce((total, subject) => {
      return total + this.testService.getSubjectQuestionCount(subject);
    }, 0);
  }

  startTest(): void {
    this.router.navigate(['/configure']);
  }

  getSubjectQuestionCount(subject: string): number {
    return this.testService.getSubjectQuestionCount(subject);
  }
}
