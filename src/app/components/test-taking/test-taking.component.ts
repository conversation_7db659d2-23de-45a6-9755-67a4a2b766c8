import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription, interval } from 'rxjs';
import { TestService } from '../../services/test.service';
import { TestSession, QuestionWithMetadata } from '../../models/question.model';

@Component({
  selector: 'app-test-taking',
  templateUrl: './test-taking.component.html',
  styleUrls: ['./test-taking.component.css']
})
export class TestTakingComponent implements OnInit, OnDestroy {
  session: TestSession | null = null;
  currentQuestion: QuestionWithMetadata | null = null;
  selectedAnswer: string = '';
  timeElapsed: number = 0;
  timeRemaining: number | null = null;
  
  private sessionSubscription: Subscription | null = null;
  private timerSubscription: Subscription | null = null;

  constructor(
    private testService: TestService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.sessionSubscription = this.testService.currentSession$.subscribe(session => {
      if (!session) {
        this.router.navigate(['/configure']);
        return;
      }
      
      this.session = session;
      this.loadCurrentQuestion();
      this.startTimer();
    });
  }

  ngOnDestroy(): void {
    if (this.sessionSubscription) {
      this.sessionSubscription.unsubscribe();
    }
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
  }

  private loadCurrentQuestion(): void {
    if (this.session) {
      this.currentQuestion = this.session.questions[this.session.currentQuestionIndex];
      this.selectedAnswer = this.session.answers[this.session.currentQuestionIndex] || '';
    }
  }

  private startTimer(): void {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }

    this.timerSubscription = interval(1000).subscribe(() => {
      if (this.session) {
        this.timeElapsed = Math.floor((new Date().getTime() - this.session.startTime.getTime()) / 1000);
        
        if (this.session.configuration.timeLimit) {
          const timeLimitSeconds = this.session.configuration.timeLimit * 60;
          this.timeRemaining = timeLimitSeconds - this.timeElapsed;
          
          if (this.timeRemaining <= 0) {
            this.completeTest();
          }
        }
      }
    });
  }

  selectAnswer(answer: string): void {
    this.selectedAnswer = answer;
    if (this.session) {
      this.testService.answerQuestion(this.session.currentQuestionIndex, answer);
    }
  }

  nextQuestion(): void {
    if (this.testService.nextQuestion()) {
      this.loadCurrentQuestion();
    } else {
      // Це останнє питання
      this.completeTest();
    }
  }

  previousQuestion(): void {
    if (this.testService.previousQuestion()) {
      this.loadCurrentQuestion();
    }
  }

  goToQuestion(index: number): void {
    if (this.testService.goToQuestion(index)) {
      this.loadCurrentQuestion();
    }
  }

  completeTest(): void {
    this.testService.completeTest();
    this.router.navigate(['/results']);
  }

  getProgress(): number {
    return this.testService.getProgress();
  }

  getAnsweredQuestionsCount(): number {
    return this.testService.getAnsweredQuestionsCount();
  }

  isQuestionAnswered(index: number): boolean {
    return this.testService.isQuestionAnswered(index);
  }

  getCurrentQuestionNumber(): number {
    return this.session ? this.session.currentQuestionIndex + 1 : 0;
  }

  getTotalQuestions(): number {
    return this.session ? this.session.questions.length : 0;
  }

  formatTime(seconds: number): string {
    return this.testService.formatTime(seconds);
  }

  canGoNext(): boolean {
    return this.session ? this.session.currentQuestionIndex < this.session.questions.length - 1 : false;
  }

  canGoPrevious(): boolean {
    return this.session ? this.session.currentQuestionIndex > 0 : false;
  }

  getQuestionOptions(): string[] {
    if (!this.currentQuestion) return [];
    
    return this.currentQuestion.shuffledOptions || this.currentQuestion.options;
  }

  isLastQuestion(): boolean {
    return this.session ? this.session.currentQuestionIndex === this.session.questions.length - 1 : false;
  }

  getTimeRemainingClass(): string {
    if (!this.timeRemaining) return '';
    
    if (this.timeRemaining < 60) return 'time-critical';
    if (this.timeRemaining < 300) return 'time-warning';
    return '';
  }

  confirmCompleteTest(): void {
    const unansweredCount = this.getTotalQuestions() - this.getAnsweredQuestionsCount();
    
    if (unansweredCount > 0) {
      const confirmed = confirm(
        `У вас залишилося ${unansweredCount} питань без відповіді. Ви впевнені, що хочете завершити тест?`
      );
      if (!confirmed) return;
    }
    
    this.completeTest();
  }
}
