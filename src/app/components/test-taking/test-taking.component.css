.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 25px;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.test-info .test-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 10px 0;
}

.test-title i {
  color: #667eea;
  margin-right: 10px;
}

.test-meta {
  display: flex;
  gap: 15px;
  align-items: center;
}

.question-counter {
  color: #6c757d;
  font-weight: 500;
}

.subject-badge {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
}

.test-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  font-weight: 500;
  color: #495057;
}

.stat-item i {
  color: #667eea;
}

.stat-item.time-warning {
  background: #fff3cd;
  color: #856404;
}

.stat-item.time-warning i {
  color: #ffc107;
}

.stat-item.time-critical {
  background: #f8d7da;
  color: #721c24;
  animation: pulse 1s infinite;
}

.stat-item.time-critical i {
  color: #dc3545;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.progress-section {
  margin-bottom: 30px;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #6c757d;
  font-weight: 500;
}

.question-section {
  margin-bottom: 30px;
}

.question-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.question-number {
  background: #667eea;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.question-subject {
  background: #f8f9fa;
  color: #6c757d;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
}

.question-text {
  font-size: 1.2rem;
  font-weight: 500;
  color: #333;
  line-height: 1.6;
  margin-bottom: 25px;
}

.options-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: #f8f9fa;
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.option-item:hover {
  background: #e9ecef;
}

.option-item.selected {
  background: #e3f2fd;
  border-color: #667eea;
}

.option-radio input[type="radio"] {
  transform: scale(1.3);
  margin: 0;
}

.option-text {
  font-size: 1rem;
  color: #333;
  line-height: 1.5;
}

.navigation-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.nav-buttons {
  display: flex;
  gap: 15px;
}

.btn-complete {
  font-size: 0.9rem;
}

.questions-map {
  background: white;
  padding: 25px;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.questions-map h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.questions-map h3 i {
  color: #667eea;
  margin-right: 8px;
}

.questions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 8px;
  margin-bottom: 20px;
}

.question-dot {
  width: 40px;
  height: 40px;
  border: 2px solid #dee2e6;
  background: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.question-dot:hover {
  transform: scale(1.1);
}

.question-dot.current {
  background: #667eea;
  color: white;
  border-color: #667eea;
  transform: scale(1.1);
}

.question-dot.answered {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.question-dot.unanswered {
  background: #f8f9fa;
  color: #6c757d;
  border-color: #dee2e6;
}

.map-legend {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #6c757d;
}

.legend-dot {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 2px solid;
}

.legend-dot.current {
  background: #667eea;
  border-color: #667eea;
}

.legend-dot.answered {
  background: #28a745;
  border-color: #28a745;
}

.legend-dot.unanswered {
  background: #f8f9fa;
  border-color: #dee2e6;
}

.no-session {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.no-session i {
  font-size: 4rem;
  color: #ffc107;
  margin-bottom: 20px;
}

.no-session h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.no-session p {
  color: #6c757d;
  font-size: 1.1rem;
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .test-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .test-stats {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .navigation-section {
    flex-direction: column;
    gap: 15px;
  }
  
  .nav-buttons {
    width: 100%;
    justify-content: space-between;
  }
  
  .questions-grid {
    grid-template-columns: repeat(auto-fill, minmax(35px, 1fr));
  }
  
  .question-dot {
    width: 35px;
    height: 35px;
    font-size: 0.8rem;
  }
  
  .map-legend {
    flex-direction: column;
    gap: 10px;
  }
}
