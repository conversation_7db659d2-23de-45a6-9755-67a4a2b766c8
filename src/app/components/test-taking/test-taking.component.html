<div class="container" *ngIf="session">
  <!-- Заголовок з прогресом -->
  <div class="test-header">
    <div class="test-info">
      <h1 class="test-title">
        <i class="fas fa-clipboard-check"></i>
        Тестування
      </h1>
      <div class="test-meta">
        <span class="question-counter">
          Питання {{ getCurrentQuestionNumber() }} з {{ getTotalQuestions() }}
        </span>
        <span class="subject-badge" *ngIf="currentQuestion">
          {{ currentQuestion.subject }}
        </span>
      </div>
    </div>
    
    <div class="test-stats">
      <div class="stat-item">
        <i class="fas fa-clock"></i>
        <span>{{ formatTime(timeElapsed) }}</span>
      </div>
      <div class="stat-item" *ngIf="timeRemaining !== null" [class]="getTimeRemainingClass()">
        <i class="fas fa-hourglass-half"></i>
        <span>{{ formatTime(timeRemaining) }}</span>
      </div>
      <div class="stat-item">
        <i class="fas fa-check-circle"></i>
        <span>{{ getAnsweredQuestionsCount() }}/{{ getTotalQuestions() }}</span>
      </div>
    </div>
  </div>

  <!-- Прогрес-бар -->
  <div class="progress-section">
    <div class="progress-bar">
      <div class="progress-fill" [style.width.%]="getProgress()"></div>
    </div>
    <div class="progress-text">
      {{ getProgress() }}% завершено
    </div>
  </div>

  <!-- Питання -->
  <div class="question-section" *ngIf="currentQuestion">
    <div class="question-card fade-in">
      <div class="question-header">
        <span class="question-number">
          Питання {{ getCurrentQuestionNumber() }}
        </span>
        <span class="question-subject">
          {{ currentQuestion.subject }}
        </span>
      </div>
      
      <div class="question-text">
        {{ currentQuestion.question }}
      </div>
      
      <div class="options-section">
        <div class="option-item" 
             *ngFor="let option of getQuestionOptions()"
             [class.selected]="selectedAnswer === option"
             (click)="selectAnswer(option)">
          <div class="option-radio">
            <input type="radio" 
                   [value]="option"
                   [checked]="selectedAnswer === option"
                   (change)="selectAnswer(option)"
                   name="answer">
          </div>
          <div class="option-text">
            {{ option }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Навігація -->
  <div class="navigation-section">
    <div class="nav-buttons">
      <button class="btn btn-secondary" 
              [disabled]="!canGoPrevious()"
              (click)="previousQuestion()">
        <i class="fas fa-chevron-left"></i>
        Попереднє
      </button>
      
      <button class="btn btn-primary" 
              *ngIf="!isLastQuestion()"
              (click)="nextQuestion()">
        Наступне
        <i class="fas fa-chevron-right"></i>
      </button>
      
      <button class="btn btn-success" 
              *ngIf="isLastQuestion()"
              (click)="confirmCompleteTest()">
        <i class="fas fa-flag-checkered"></i>
        Завершити тест
      </button>
    </div>
    
    <button class="btn btn-danger btn-complete" 
            (click)="confirmCompleteTest()">
      <i class="fas fa-stop"></i>
      Завершити достроково
    </button>
  </div>

  <!-- Міні-карта питань -->
  <div class="questions-map">
    <h3>
      <i class="fas fa-map"></i>
      Карта питань
    </h3>
    <div class="questions-grid">
      <button class="question-dot"
              *ngFor="let question of session.questions; let i = index"
              [class.current]="i === session.currentQuestionIndex"
              [class.answered]="isQuestionAnswered(i)"
              [class.unanswered]="!isQuestionAnswered(i)"
              (click)="goToQuestion(i)"
              [title]="'Питання ' + (i + 1) + (isQuestionAnswered(i) ? ' (відповідь дана)' : ' (без відповіді)')">
        {{ i + 1 }}
      </button>
    </div>
    <div class="map-legend">
      <div class="legend-item">
        <span class="legend-dot current"></span>
        <span>Поточне</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot answered"></span>
        <span>Відповідь дана</span>
      </div>
      <div class="legend-item">
        <span class="legend-dot unanswered"></span>
        <span>Без відповіді</span>
      </div>
    </div>
  </div>
</div>

<!-- Заглушка якщо немає сесії -->
<div class="container" *ngIf="!session">
  <div class="no-session">
    <i class="fas fa-exclamation-triangle"></i>
    <h2>Тест не знайдено</h2>
    <p>Будь ласка, налаштуйте тест перед початком.</p>
    <button class="btn btn-primary" routerLink="/configure">
      <i class="fas fa-cog"></i>
      Налаштувати тест
    </button>
  </div>
</div>
