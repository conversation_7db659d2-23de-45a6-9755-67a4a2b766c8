<div class="container" *ngIf="result">
  <!-- Заголовок результатів -->
  <div class="results-header">
    <h1 class="page-title">
      <i class="fas fa-trophy"></i>
      Результати тестування
    </h1>
    <div class="header-actions">
      <button class="btn btn-secondary" (click)="downloadResults()">
        <i class="fas fa-download"></i>
        Завантажити
      </button>
      <button class="btn btn-secondary" (click)="printResults()">
        <i class="fas fa-print"></i>
        Друк
      </button>
    </div>
  </div>

  <!-- Основний результат -->
  <div class="main-result-card">
    <div class="score-section">
      <div class="score-circle" [class]="getScoreClass()">
        <div class="score-percentage">{{ result.percentage }}%</div>
        <div class="score-message">{{ getScoreMessage() }}</div>
      </div>
      
      <div class="score-details">
        <div class="score-stat">
          <span class="stat-number">{{ result.correctAnswers }}</span>
          <span class="stat-label">Правильних відповідей</span>
        </div>
        <div class="score-stat">
          <span class="stat-number">{{ result.totalQuestions }}</span>
          <span class="stat-label">Загалом питань</span>
        </div>
        <div class="score-stat">
          <span class="stat-number">{{ formatTime(result.timeSpent) }}</span>
          <span class="stat-label">Час проходження</span>
        </div>
        <div class="score-stat">
          <span class="stat-number">{{ getAverageTimePerQuestion() }}</span>
          <span class="stat-label">Середній час на питання</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Результати по предметах -->
  <div class="subjects-results">
    <h2 class="section-title">
      <i class="fas fa-chart-bar"></i>
      Результати по предметах
    </h2>
    
    <div class="subjects-grid">
      <div class="subject-result-card" 
           *ngFor="let subject of result.subjectResults"
           [class]="getSubjectPercentageClass(subject.percentage)">
        <div class="subject-header">
          <h3>{{ subject.subject }}</h3>
          <span class="subject-percentage">{{ subject.percentage }}%</span>
        </div>
        
        <div class="subject-stats">
          <div class="subject-progress">
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="subject.percentage"></div>
            </div>
          </div>
          
          <div class="subject-numbers">
            <span>{{ subject.correct }} з {{ subject.total }} питань</span>
          </div>
        </div>
        
        <button class="btn btn-outline btn-sm" 
                (click)="selectSubject(subject.subject)">
          <i class="fas fa-eye"></i>
          {{ selectedSubject === subject.subject ? 'Сховати деталі' : 'Показати деталі' }}
        </button>
        
        <!-- Детальні результати по предмету -->
        <div class="subject-details" *ngIf="selectedSubject === subject.subject">
          <div class="question-result" 
               *ngFor="let question of getSubjectQuestions(subject.subject); let i = index"
               [class.correct]="question.isCorrect"
               [class.incorrect]="!question.isCorrect">
            <div class="question-number">{{ i + 1 }}</div>
            <div class="question-content">
              <div class="question-text">{{ question.question.question }}</div>
              <div class="answer-comparison">
                <div class="user-answer">
                  <strong>Ваша відповідь:</strong> 
                  <span [class.empty]="!question.userAnswer">
                    {{ question.userAnswer || 'Не відповіли' }}
                  </span>
                </div>
                <div class="correct-answer">
                  <strong>Правильна відповідь:</strong> {{ question.correctAnswer }}
                </div>
              </div>
            </div>
            <div class="question-status">
              <i [class]="question.isCorrect ? 'fas fa-check-circle correct-icon' : 'fas fa-times-circle incorrect-icon'"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Детальні результати -->
  <div class="detailed-section">
    <div class="section-header">
      <h2 class="section-title">
        <i class="fas fa-list-alt"></i>
        Детальні результати
      </h2>
      <button class="btn btn-outline" (click)="toggleDetailedResults()">
        <i [class]="showDetailedResults ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
        {{ showDetailedResults ? 'Сховати' : 'Показати' }} всі питання
      </button>
    </div>
    
    <div class="all-questions" *ngIf="showDetailedResults">
      <div class="subject-group" *ngFor="let subject of result.subjectResults">
        <h3 class="subject-group-title">{{ subject.subject }}</h3>
        
        <div class="question-result detailed" 
             *ngFor="let question of subject.questions; let i = index"
             [class.correct]="question.isCorrect"
             [class.incorrect]="!question.isCorrect">
          <div class="question-header-detailed">
            <span class="question-number-detailed">Питання {{ i + 1 }}</span>
            <span class="question-status-badge" [class]="question.isCorrect ? 'correct' : 'incorrect'">
              <i [class]="question.isCorrect ? 'fas fa-check' : 'fas fa-times'"></i>
              {{ question.isCorrect ? 'Правильно' : 'Неправильно' }}
            </span>
          </div>
          
          <div class="question-text-detailed">{{ question.question.question }}</div>
          
          <div class="options-detailed">
            <div class="option-detailed" 
                 *ngFor="let option of question.question.options"
                 [class.user-selected]="option === question.userAnswer"
                 [class.correct-option]="option === question.correctAnswer"
                 [class.wrong-selection]="option === question.userAnswer && !question.isCorrect">
              <span class="option-text">{{ option }}</span>
              <span class="option-indicators">
                <i class="fas fa-check correct-indicator" *ngIf="option === question.correctAnswer"></i>
                <i class="fas fa-user user-indicator" *ngIf="option === question.userAnswer"></i>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Дії -->
  <div class="actions-section">
    <button class="btn btn-primary btn-large" (click)="startNewTest()">
      <i class="fas fa-redo"></i>
      Пройти новий тест
    </button>
    <button class="btn btn-secondary btn-large" (click)="goHome()">
      <i class="fas fa-home"></i>
      На головну
    </button>
  </div>
</div>

<!-- Заглушка якщо немає результатів -->
<div class="container" *ngIf="!result">
  <div class="no-results">
    <i class="fas fa-exclamation-triangle"></i>
    <h2>Результати не знайдено</h2>
    <p>Будь ласка, пройдіть тест щоб побачити результати.</p>
    <button class="btn btn-primary" routerLink="/configure">
      <i class="fas fa-play"></i>
      Почати тест
    </button>
  </div>
</div>
