.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 2.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.page-title i {
  color: #ffc107;
  margin-right: 10px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.main-result-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 40px;
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
}

.score-section {
  display: flex;
  align-items: center;
  gap: 40px;
}

.score-circle {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.score-circle.excellent {
  background: rgba(40, 167, 69, 0.2);
  border-color: rgba(40, 167, 69, 0.5);
}

.score-circle.good {
  background: rgba(23, 162, 184, 0.2);
  border-color: rgba(23, 162, 184, 0.5);
}

.score-circle.satisfactory {
  background: rgba(255, 193, 7, 0.2);
  border-color: rgba(255, 193, 7, 0.5);
}

.score-circle.poor {
  background: rgba(220, 53, 69, 0.2);
  border-color: rgba(220, 53, 69, 0.5);
}

.score-percentage {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.score-message {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.9;
}

.score-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  flex: 1;
}

.score-stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.subjects-results {
  margin-bottom: 40px;
}

.section-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 25px;
}

.section-title i {
  color: #667eea;
  margin-right: 10px;
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.subject-result-card {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-left: 5px solid #dee2e6;
  transition: transform 0.3s ease;
}

.subject-result-card:hover {
  transform: translateY(-3px);
}

.subject-result-card.excellent {
  border-left-color: #28a745;
}

.subject-result-card.good {
  border-left-color: #17a2b8;
}

.subject-result-card.satisfactory {
  border-left-color: #ffc107;
}

.subject-result-card.poor {
  border-left-color: #dc3545;
}

.subject-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.subject-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.subject-percentage {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.subject-stats {
  margin-bottom: 20px;
}

.subject-progress {
  margin-bottom: 10px;
}

.subject-numbers {
  color: #6c757d;
  font-weight: 500;
  text-align: center;
}

.subject-details {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
}

.question-result {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 10px;
  background: #f8f9fa;
}

.question-result.correct {
  background: #d4edda;
  border-left: 4px solid #28a745;
}

.question-result.incorrect {
  background: #f8d7da;
  border-left: 4px solid #dc3545;
}

.question-number {
  background: #667eea;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.question-content {
  flex: 1;
}

.question-text {
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
}

.answer-comparison {
  font-size: 0.9rem;
}

.user-answer, .correct-answer {
  margin-bottom: 5px;
}

.user-answer .empty {
  color: #6c757d;
  font-style: italic;
}

.question-status {
  flex-shrink: 0;
}

.correct-icon {
  color: #28a745;
  font-size: 1.2rem;
}

.incorrect-icon {
  color: #dc3545;
  font-size: 1.2rem;
}

.detailed-section {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.subject-group {
  margin-bottom: 30px;
}

.subject-group-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.question-result.detailed {
  flex-direction: column;
  gap: 15px;
}

.question-header-detailed {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-number-detailed {
  font-weight: 600;
  color: #495057;
}

.question-status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.question-status-badge.correct {
  background: #d4edda;
  color: #155724;
}

.question-status-badge.incorrect {
  background: #f8d7da;
  color: #721c24;
}

.question-text-detailed {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
  line-height: 1.5;
}

.options-detailed {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-detailed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-radius: 8px;
  background: white;
  border: 1px solid #dee2e6;
}

.option-detailed.correct-option {
  background: #d4edda;
  border-color: #28a745;
}

.option-detailed.wrong-selection {
  background: #f8d7da;
  border-color: #dc3545;
}

.option-detailed.user-selected:not(.wrong-selection) {
  background: #cce5ff;
  border-color: #667eea;
}

.option-indicators {
  display: flex;
  gap: 8px;
}

.correct-indicator {
  color: #28a745;
}

.user-indicator {
  color: #667eea;
}

.actions-section {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 40px 0;
}

.btn-large {
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.no-results i {
  font-size: 4rem;
  color: #ffc107;
  margin-bottom: 20px;
}

.no-results h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.no-results p {
  color: #6c757d;
  font-size: 1.1rem;
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .results-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .score-section {
    flex-direction: column;
    gap: 30px;
    text-align: center;
  }
  
  .score-details {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .subjects-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .actions-section {
    flex-direction: column;
    align-items: center;
  }
  
  .btn-large {
    width: 100%;
    max-width: 300px;
  }
}
