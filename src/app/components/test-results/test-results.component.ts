import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TestService } from '../../services/test.service';
import { TestResult, SubjectResult, QuestionResult } from '../../models/question.model';

@Component({
  selector: 'app-test-results',
  templateUrl: './test-results.component.html',
  styleUrls: ['./test-results.component.css']
})
export class TestResultsComponent implements OnInit {
  result: TestResult | null = null;
  showDetailedResults: boolean = false;
  selectedSubject: string | null = null;

  constructor(
    private testService: TestService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.result = this.testService.getTestResult();
    
    if (!this.result) {
      this.router.navigate(['/']);
    }
  }

  getScoreClass(): string {
    if (!this.result) return '';
    
    const percentage = this.result.percentage;
    if (percentage >= 90) return 'excellent';
    if (percentage >= 75) return 'good';
    if (percentage >= 60) return 'satisfactory';
    return 'poor';
  }

  getScoreMessage(): string {
    if (!this.result) return '';
    
    const percentage = this.result.percentage;
    if (percentage >= 90) return 'Відмінно!';
    if (percentage >= 75) return 'Добре!';
    if (percentage >= 60) return 'Задовільно';
    return 'Потрібно покращити знання';
  }

  toggleDetailedResults(): void {
    this.showDetailedResults = !this.showDetailedResults;
  }

  selectSubject(subject: string): void {
    this.selectedSubject = this.selectedSubject === subject ? null : subject;
  }

  getSubjectQuestions(subject: string): QuestionResult[] {
    if (!this.result) return [];
    
    const subjectResult = this.result.subjectResults.find(s => s.subject === subject);
    return subjectResult ? subjectResult.questions : [];
  }

  formatTime(seconds: number): string {
    return this.testService.formatTime(seconds);
  }

  getAverageTimePerQuestion(): string {
    if (!this.result || this.result.totalQuestions === 0) return '0:00';
    
    const avgSeconds = Math.round(this.result.timeSpent / this.result.totalQuestions);
    return this.formatTime(avgSeconds);
  }

  startNewTest(): void {
    this.testService.resetTest();
    this.router.navigate(['/configure']);
  }

  goHome(): void {
    this.testService.resetTest();
    this.router.navigate(['/']);
  }

  getSubjectPercentageClass(percentage: number): string {
    if (percentage >= 90) return 'excellent';
    if (percentage >= 75) return 'good';
    if (percentage >= 60) return 'satisfactory';
    return 'poor';
  }

  downloadResults(): void {
    if (!this.result) return;

    const content = this.generateResultsText();
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `test-results-${new Date().toISOString().split('T')[0]}.txt`;
    link.click();
    
    window.URL.revokeObjectURL(url);
  }

  private generateResultsText(): string {
    if (!this.result) return '';

    let content = `РЕЗУЛЬТАТИ ТЕСТУВАННЯ\n`;
    content += `========================\n\n`;
    content += `Дата: ${new Date().toLocaleDateString('uk-UA')}\n`;
    content += `Час проходження: ${this.formatTime(this.result.timeSpent)}\n`;
    content += `Загальний результат: ${this.result.correctAnswers}/${this.result.totalQuestions} (${this.result.percentage}%)\n\n`;

    content += `РЕЗУЛЬТАТИ ПО ПРЕДМЕТАХ:\n`;
    content += `------------------------\n`;
    this.result.subjectResults.forEach(subject => {
      content += `${subject.subject}: ${subject.correct}/${subject.total} (${subject.percentage}%)\n`;
    });

    if (this.showDetailedResults) {
      content += `\n\nДЕТАЛЬНІ РЕЗУЛЬТАТИ:\n`;
      content += `====================\n`;
      
      this.result.subjectResults.forEach(subject => {
        content += `\n${subject.subject.toUpperCase()}:\n`;
        content += `-`.repeat(subject.subject.length + 1) + `\n`;
        
        subject.questions.forEach((q, index) => {
          content += `\n${index + 1}. ${q.question.question}\n`;
          content += `   Ваша відповідь: ${q.userAnswer || 'Не відповіли'}\n`;
          content += `   Правильна відповідь: ${q.correctAnswer}\n`;
          content += `   Результат: ${q.isCorrect ? 'Правильно ✓' : 'Неправильно ✗'}\n`;
        });
      });
    }

    return content;
  }

  printResults(): void {
    window.print();
  }
}
