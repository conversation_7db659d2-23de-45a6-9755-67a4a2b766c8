import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TestService } from '../../services/test.service';
import { TestConfiguration } from '../../models/question.model';

@Component({
  selector: 'app-test-configuration',
  templateUrl: './test-configuration.component.html',
  styleUrls: ['./test-configuration.component.css']
})
export class TestConfigurationComponent implements OnInit {
  configForm: FormGroup;
  availableSubjects: string[] = [];
  selectedSubjects: Set<string> = new Set();
  questionsPerSubject: { [subject: string]: number } = {};
  totalQuestions: number = 0;
  maxQuestionsPerSubject: { [subject: string]: number } = {};

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private testService: TestService
  ) {
    this.configForm = this.fb.group({
      shuffleQuestions: [true],
      shuffleOptions: [true],
      timeLimit: [null, [Validators.min(1), Validators.max(180)]]
    });
  }

  ngOnInit(): void {
    this.availableSubjects = this.testService.getAvailableSubjects();
    
    // Ініціалізуємо максимальну кількість питань для кожного предмету
    this.availableSubjects.forEach(subject => {
      this.maxQuestionsPerSubject[subject] = this.testService.getSubjectQuestionCount(subject);
      this.questionsPerSubject[subject] = 0;
    });
  }

  toggleSubject(subject: string): void {
    if (this.selectedSubjects.has(subject)) {
      this.selectedSubjects.delete(subject);
      this.questionsPerSubject[subject] = 0;
    } else {
      this.selectedSubjects.add(subject);
      this.questionsPerSubject[subject] = Math.min(5, this.maxQuestionsPerSubject[subject]);
    }
    this.updateTotalQuestions();
  }

  isSubjectSelected(subject: string): boolean {
    return this.selectedSubjects.has(subject);
  }

  updateQuestionCount(subject: string, count: number): void {
    if (this.selectedSubjects.has(subject)) {
      this.questionsPerSubject[subject] = Math.max(0, Math.min(count, this.maxQuestionsPerSubject[subject]));
      this.updateTotalQuestions();
    }
  }

  private updateTotalQuestions(): void {
    this.totalQuestions = Array.from(this.selectedSubjects).reduce((total, subject) => {
      return total + this.questionsPerSubject[subject];
    }, 0);
  }

  selectAllSubjects(): void {
    this.availableSubjects.forEach(subject => {
      this.selectedSubjects.add(subject);
      this.questionsPerSubject[subject] = Math.min(5, this.maxQuestionsPerSubject[subject]);
    });
    this.updateTotalQuestions();
  }

  clearAllSubjects(): void {
    this.selectedSubjects.clear();
    this.availableSubjects.forEach(subject => {
      this.questionsPerSubject[subject] = 0;
    });
    this.updateTotalQuestions();
  }

  setQuickConfig(questionsPerSubject: number): void {
    Array.from(this.selectedSubjects).forEach(subject => {
      this.questionsPerSubject[subject] = Math.min(questionsPerSubject, this.maxQuestionsPerSubject[subject]);
    });
    this.updateTotalQuestions();
  }

  canStartTest(): boolean {
    return this.selectedSubjects.size > 0 && this.totalQuestions > 0;
  }

  startTest(): void {
    if (!this.canStartTest()) {
      return;
    }

    const configuration: TestConfiguration = {
      selectedSubjects: Array.from(this.selectedSubjects),
      questionsPerSubject: { ...this.questionsPerSubject },
      totalQuestions: this.totalQuestions,
      timeLimit: this.configForm.value.timeLimit,
      shuffleQuestions: this.configForm.value.shuffleQuestions,
      shuffleOptions: this.configForm.value.shuffleOptions
    };

    this.testService.createTestSession(configuration);
    this.router.navigate(['/test']);
  }

  goBack(): void {
    this.router.navigate(['/']);
  }
}
