.config-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.page-title i {
  color: #667eea;
  margin-right: 10px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f8f9fa;
}

.card-header h2 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.card-header h2 i {
  color: #667eea;
  margin-right: 8px;
}

.quick-actions {
  display: flex;
  gap: 10px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.9rem;
}

.subjects-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 25px;
}

.subject-item {
  background: #f8f9fa;
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.subject-item:hover {
  background: #e9ecef;
}

.subject-item.selected {
  background: #e3f2fd;
  border-color: #667eea;
}

.subject-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.subject-checkbox input[type="checkbox"] {
  transform: scale(1.3);
  margin: 0;
}

.subject-details h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
}

.subject-details p {
  color: #6c757d;
  margin: 0;
  font-size: 0.9rem;
}

.question-counter {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-top: 15px;
  border-top: 1px solid #dee2e6;
}

.question-counter label {
  font-weight: 500;
  color: #495057;
  margin: 0;
}

.counter-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-counter {
  width: 32px;
  height: 32px;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-counter:hover {
  background: #f8f9fa;
  border-color: #667eea;
}

.counter-input {
  width: 60px;
  text-align: center;
  padding: 6px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-weight: 500;
}

.quick-configs {
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
}

.quick-configs h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 15px;
}

.quick-config-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn-outline {
  background: transparent;
  border: 2px solid #667eea;
  color: #667eea;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
}

.settings-grid {
  display: grid;
  gap: 25px;
}

.setting-item {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  margin-bottom: 8px;
}

.setting-label input[type="checkbox"] {
  transform: scale(1.2);
}

.setting-description {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

.summary-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}

.summary-stats {
  display: flex;
  gap: 30px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.summary-actions .btn-large {
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  background: white;
  color: #667eea;
  border: none;
}

.summary-actions .btn-large:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
}

.summary-actions .btn-large:disabled {
  background: rgba(255, 255, 255, 0.5);
  color: rgba(102, 126, 234, 0.5);
  cursor: not-allowed;
  transform: none;
}

.summary-warning {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  margin-top: 20px;
}

.summary-warning i {
  margin-right: 8px;
}

@media (max-width: 768px) {
  .config-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .quick-actions {
    width: 100%;
    justify-content: center;
  }
  
  .subject-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .question-counter {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .summary-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .summary-stats {
    justify-content: center;
    width: 100%;
  }
}
