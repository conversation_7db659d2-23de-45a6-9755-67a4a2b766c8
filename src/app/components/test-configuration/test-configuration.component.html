<div class="container">
  <div class="config-header">
    <button class="btn btn-secondary" (click)="goBack()">
      <i class="fas fa-arrow-left"></i>
      Назад
    </button>
    <h1 class="page-title">
      <i class="fas fa-cog"></i>
      Налаштування тесту
    </h1>
  </div>

  <form [formGroup]="configForm" class="config-form">
    <!-- Вибір предметів -->
    <div class="card">
      <div class="card-header">
        <h2>
          <i class="fas fa-book"></i>
          Вибір предметів
        </h2>
        <div class="quick-actions">
          <button type="button" class="btn btn-secondary btn-sm" (click)="selectAllSubjects()">
            <i class="fas fa-check-double"></i>
            Вибрати всі
          </button>
          <button type="button" class="btn btn-secondary btn-sm" (click)="clearAllSubjects()">
            <i class="fas fa-times"></i>
            Очистити
          </button>
        </div>
      </div>

      <div class="subjects-list">
        <div class="subject-item" 
             *ngFor="let subject of availableSubjects"
             [class.selected]="isSubjectSelected(subject)"
             (click)="toggleSubject(subject)">
          <div class="subject-info">
            <div class="subject-checkbox">
              <input type="checkbox" 
                     [checked]="isSubjectSelected(subject)"
                     (click)="$event.stopPropagation()">
            </div>
            <div class="subject-details">
              <h3>{{ subject }}</h3>
              <p>Доступно {{ maxQuestionsPerSubject[subject] }} питань</p>
            </div>
          </div>
          
          <div class="question-counter" *ngIf="isSubjectSelected(subject)">
            <label>Кількість питань:</label>
            <div class="counter-controls">
              <button type="button" 
                      class="btn-counter" 
                      (click)="updateQuestionCount(subject, questionsPerSubject[subject] - 1); $event.stopPropagation()">
                <i class="fas fa-minus"></i>
              </button>
              <input type="number" 
                     [value]="questionsPerSubject[subject]"
                     (input)="updateQuestionCount(subject, +$event.target.value)"
                     (click)="$event.stopPropagation()"
                     [min]="1"
                     [max]="maxQuestionsPerSubject[subject]"
                     class="counter-input">
              <button type="button" 
                      class="btn-counter" 
                      (click)="updateQuestionCount(subject, questionsPerSubject[subject] + 1); $event.stopPropagation()">
                <i class="fas fa-plus"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="quick-configs" *ngIf="selectedSubjects.size > 0">
        <h3>Швидкі налаштування:</h3>
        <div class="quick-config-buttons">
          <button type="button" class="btn btn-outline" (click)="setQuickConfig(3)">
            По 3 питання
          </button>
          <button type="button" class="btn btn-outline" (click)="setQuickConfig(5)">
            По 5 питань
          </button>
          <button type="button" class="btn btn-outline" (click)="setQuickConfig(10)">
            По 10 питань
          </button>
        </div>
      </div>
    </div>

    <!-- Додаткові налаштування -->
    <div class="card">
      <div class="card-header">
        <h2>
          <i class="fas fa-sliders-h"></i>
          Додаткові налаштування
        </h2>
      </div>

      <div class="settings-grid">
        <div class="setting-item">
          <label class="setting-label">
            <input type="checkbox" formControlName="shuffleQuestions">
            <span class="checkmark"></span>
            Перемішувати питання
          </label>
          <p class="setting-description">Питання будуть показані у випадковому порядку</p>
        </div>

        <div class="setting-item">
          <label class="setting-label">
            <input type="checkbox" formControlName="shuffleOptions">
            <span class="checkmark"></span>
            Перемішувати варіанти відповідей
          </label>
          <p class="setting-description">Варіанти відповідей будуть показані у випадковому порядку</p>
        </div>

        <div class="setting-item">
          <label class="form-label">Обмеження часу (хвилини):</label>
          <input type="number" 
                 formControlName="timeLimit" 
                 class="form-control"
                 placeholder="Без обмежень"
                 min="1"
                 max="180">
          <p class="setting-description">Залиште порожнім для тестування без обмеження часу</p>
        </div>
      </div>
    </div>

    <!-- Підсумок -->
    <div class="card summary-card">
      <div class="summary-content">
        <div class="summary-stats">
          <div class="stat">
            <span class="stat-number">{{ selectedSubjects.size }}</span>
            <span class="stat-label">Предметів</span>
          </div>
          <div class="stat">
            <span class="stat-number">{{ totalQuestions }}</span>
            <span class="stat-label">Питань</span>
          </div>
          <div class="stat" *ngIf="configForm.value.timeLimit">
            <span class="stat-number">{{ configForm.value.timeLimit }}</span>
            <span class="stat-label">Хвилин</span>
          </div>
        </div>

        <div class="summary-actions">
          <button type="button" 
                  class="btn btn-primary btn-large"
                  [disabled]="!canStartTest()"
                  (click)="startTest()">
            <i class="fas fa-play"></i>
            Почати тест
          </button>
        </div>
      </div>

      <div class="summary-warning" *ngIf="!canStartTest()">
        <i class="fas fa-exclamation-triangle"></i>
        Виберіть хоча б один предмет та встановіть кількість питань
      </div>
    </div>
  </form>
</div>
