import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { HomeComponent } from './components/home/<USER>';
import { TestConfigurationComponent } from './components/test-configuration/test-configuration.component';
import { TestTakingComponent } from './components/test-taking/test-taking.component';
import { TestResultsComponent } from './components/test-results/test-results.component';

const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'configure', component: TestConfigurationComponent },
  { path: 'test', component: TestTakingComponent },
  { path: 'results', component: TestResultsComponent },
  { path: '**', redirectTo: '' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
