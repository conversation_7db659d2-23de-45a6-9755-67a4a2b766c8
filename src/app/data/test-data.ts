import { TestData } from '../models/question.model';

export const testData: TestData = {
  "Дискретна математика": [
    {
      question: "Який логічний вираз є еквівалентним до A ∧ ABC ∧ ABCD?",
      options: ["A) A ∧ BCD", "B) A ∧ BC", "C) A", "D) 0"],
      correct: "C"
    },
    {
      question: "Скільки існує логічних функцій від двох змінних?",
      options: ["A) 2", "B) 4", "C) 8", "D) 16"],
      correct: "D"
    },
    {
      question: "Яка з формул може бути спрощена?",
      options: ["A) yz ∨ ȳz̄", "B) z̄ ∨ x ∧ y", "C) yz ∨ z̄ ∧ ȳ", "D) z̄ ∨ yz"],
      correct: "A"
    },
    {
      question: "Для того щоб зв'язаний граф містив ейлеровий цикл, необхідно і достатньо:",
      options: [
        "A) ступені всіх вершин були непарними",
        "B) ступені двох вершин графу були парними, а інших - непарними", 
        "C) ступені всіх вершин були парними",
        "D) ступені двох вершин графу були непарними, а інших - парними"
      ],
      correct: "C"
    },
    {
      question: "Які операції булеві?",
      options: ["A) ∧, ∨, →", "B) ∧, ∨", "C) ∨, →", "D) ∧, ∨, ¬"],
      correct: "D"
    },
    {
      question: "Однорідний граф G містить 15 ребер, а ступінь кожної його вершини дорівнює 5. Визначте кількість вершин графу G:",
      options: ["A) 15", "B) 12", "C) 9", "D) 6"],
      correct: "D"
    },
    {
      question: "Яке значення буде мати шістнадцяткове число 16₁₆ в десятковій системі числення?",
      options: ["A) 14", "B) 16", "C) 20", "D) 22"],
      correct: "D"
    },
    {
      question: "Яке значення буде мати число 30₁₀ в двійковій системі числення?",
      options: ["A) 011110", "B) 011111", "C) 011101", "D) 111100"],
      correct: "A"
    }
  ],
  "Теорія алгоритмів": [
    {
      question: "Операція додавання елементів до стеку з n елементів виконується за час:",
      options: ["A) O(1)", "B) O(n)", "C) O(n+1)", "D) O(n²)"],
      correct: "A"
    },
    {
      question: "Операція видалення елементів черги з n елементів виконується за час:",
      options: ["A) O(1)", "B) O(n)", "C) O(n+1)", "D) O(n²)"],
      correct: "A"
    },
    {
      question: "Черга – це структура даних...",
      options: [
        "A) з довільним доступом до елементів",
        "B) з обмеженим доступом до елементів",
        "C) з випадковим доступом до елементів",
        "D) зі спільним доступом до елементів"
      ],
      correct: "B"
    },
    {
      question: "Властивість алгоритму «масовість» визначається як:",
      options: [
        "A) алгоритм може бути використаний для розв'язання цілого класу однотипових задач",
        "B) алгоритм може бути виконаний ким завгодно",
        "C) алгоритм виконується хоча б на одному наборі елементів з області інтерпретації",
        "D) алгоритм не виконується хоча б на одному наборі елементів з області інтерпретації"
      ],
      correct: "A"
    },
    {
      question: "Властивість алгоритму «дискретність» визначається як:",
      options: [
        "A) алгоритм виконується на будь-якому наборі елементів з області інтерпретації",
        "B) можливість розбиття алгоритму на скінченну кількість етапів",
        "C) алгоритм виконується хоча б на одному наборі елементів з області інтерпретації",
        "D) алгоритм не виконується хоча б на одному наборі елементів з області інтерпретації"
      ],
      correct: "B"
    },
    {
      question: "В основі алгоритму сортування злиттям лежить принцип:",
      options: [
        "A) гілок і меж",
        "B) розділяй та володарюй", 
        "C) гілок та границь",
        "D) де Моргана"
      ],
      correct: "B"
    }
  ],
  "Об'єктно-орієнтоване програмування мовою С++": [
    {
      question: "За допомогою чого реалізується принцип поліморфізму в С++?",
      options: [
        "A) множинного наслідування",
        "B) віртуальних методів",
        "C) віртуального наслідування",
        "D) абстрактних класів"
      ],
      correct: "B"
    },
    {
      question: "В програмі описано клас і об'єкт: class A {public: int a, b, c; }; A *obj; Як звернутися до атрибуту c?",
      options: ["A) obj.c", "B) obj->c", "C) obj A -> -> c", "D) obj-> A.c"],
      correct: "B"
    },
    {
      question: "Яка з наведених функцій НЕ може бути конструктором?",
      options: ["A) void String()", "B) String();", "C) String(String & s)", "D) String(const int a)"],
      correct: "A"
    },
    {
      question: "Принцип об'єктно-орієнтованого програмування, який полягає в об'єднанні атрибутів і методів об'єкту з метою забезпечення збереженості даних, називається:",
      options: ["A) Наслідування", "B) Поліформізм", "C) Ініціалізація", "D) Інкапсуляція"],
      correct: "D"
    },
    {
      question: "Яка функція, не будучи компонентом класу, має доступ до його захищених та внутрішніх компонентів?",
      options: ["A) Шаблонна", "B) Поліморфна", "C) Дружня", "D) Статистична"],
      correct: "C"
    },
    {
      question: "Вкажіть правильне визначення віртуального методу, який приймає одне цілочисельне значення і повертає void:",
      options: [
        "A) virtual void SomeFunction(int x);",
        "B) void SomeFunction(int x) virtual;",
        "C) virtual SomeFunction(int x);",
        "D) virtual void SomeFunction(int * x);"
      ],
      correct: "A"
    }
  ],
  "Чисельні методи": [
    {
      question: "Формула x_{n+1} = x_n - f(x_n)/f'(x_n) дозволяє розв'язувати нелінійні рівняння за методом:",
      options: ["A) ітерацій", "B) ділення навпіл", "C) Ньютона", "D) хорд"],
      correct: "C"
    },
    {
      question: "Для наближеного обчислення інтеграла за методом Сімпсона крива підінтегральної функції замінюється на:",
      options: [
        "A) прямі лінії",
        "B) відрізки квадратичних парабол",
        "C) лінійний сплайн",
        "D) квадратичний сплайн"
      ],
      correct: "B"
    },
    {
      question: "Для того щоби система лінійних алгебраїчних рівнянь (СЛАР) мала єдиний розв'язок, потрібне виконання умови:",
      options: [
        "A) вільні члени СЛАР дорівнюють 0",
        "B) діагональні коефіцієнти СЛАР не дорівнюють 0",
        "C) Визначник матриці СЛАР не дорівнює 0",
        "D) коефіцієнти СЛАР невід'ємні"
      ],
      correct: "C"
    },
    {
      question: "Однокроковий метод для чисельного розв'язання задачі Коші для звичайного диференціального рівняння першого порядку – це:",
      options: [
        "A) метод Адамса",
        "B) метод Ейлера",
        "C) метод прогнозу і корекції",
        "D) метод стрільби"
      ],
      correct: "B"
    },
    {
      question: "До точних методів розв'язання систем лінійних алгебраїчних рівнянь належить:",
      options: [
        "A) метод прогонки",
        "B) метод Зейделя",
        "C) метод найменших квадратів",
        "D) метод Ньютона"
      ],
      correct: "A"
    },
    {
      question: "Для побудови інтерполюючого кубічного сплайну на проміжку [a,b] необхідно задати додатково:",
      options: [
        "A) дві граничні умови",
        "B) середнє арифметичне значення аргументів апроксимованої функції",
        "C) середнє геометричне значення аргументів апроксимованої функції",
        "D) середнє гармонійне значення аргументів апроксимованої функції"
      ],
      correct: "A"
    }
  ],
  "Організація баз даних та знань": [
    {
      question: "Виберіть термін, що НЕ належать до методології DFD:",
      options: [
        "A) зовнішня (по відношенню до системи) сутність",
        "B) процес",
        "C) потік даних",
        "D) атрибут"
      ],
      correct: "D"
    },
    {
      question: "Виберіть термін, який відноситься до моделі ERD:",
      options: [
        "A) зовнішня (по відношенню до системи) сутність",
        "B) сутність",
        "C) потік даних",
        "D) процес"
      ],
      correct: "B"
    },
    {
      question: "Який результат повертає команда SQL: SELECT 12*sal+comm FROM emp; Якщо таблиця EMP має один рядок, в якому sal дорівнює 1000, а comm - NULL:",
      options: ["A) 12000", "B) NULL", "C) помилку", "D) порожній набір даних"],
      correct: "B"
    },
    {
      question: "Який з перерахованих виразів істиний?",
      options: [
        "A) NULL = NULL",
        "B) NULL != NULL",
        "C) NULL <> NULL",
        "D) Всі перелічені вирази є хибними"
      ],
      correct: "D"
    },
    {
      question: "Укажіть базову структуру реляційної моделі даних:",
      options: ["A) функція", "B) залежність", "C) відношення", "D) ребро графа"],
      correct: "C"
    },
    {
      question: "Таблиця table1 містить 5 рядків, таблиця table2 - 7 рядків. Скільки рядків поверне запит: SELECT * FROM table1, table2;",
      options: ["A) 0 - у запиті помилка", "B) 5 рядків", "C) 12 рядків", "D) 35 рядків"],
      correct: "D"
    }
  ]
};
