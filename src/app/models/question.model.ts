export interface Question {
  question: string;
  options: string[];
  correct: string;
}

export interface TestSubject {
  name: string;
  questions: Question[];
}

export interface TestData {
  [subject: string]: Question[];
}

export interface TestConfiguration {
  selectedSubjects: string[];
  questionsPerSubject: { [subject: string]: number };
  totalQuestions: number;
  timeLimit?: number; // в хвилинах
  shuffleQuestions: boolean;
  shuffleOptions: boolean;
}

export interface TestSession {
  configuration: TestConfiguration;
  questions: QuestionWithMetadata[];
  currentQuestionIndex: number;
  answers: { [questionIndex: number]: string };
  startTime: Date;
  endTime?: Date;
  isCompleted: boolean;
}

export interface QuestionWithMetadata extends Question {
  subject: string;
  originalIndex: number;
  shuffledOptions?: string[];
}

export interface TestResult {
  session: TestSession;
  score: number;
  percentage: number;
  correctAnswers: number;
  totalQuestions: number;
  timeSpent: number; // в секундах
  subjectResults: SubjectResult[];
}

export interface SubjectResult {
  subject: string;
  correct: number;
  total: number;
  percentage: number;
  questions: QuestionResult[];
}

export interface QuestionResult {
  question: Question;
  userAnswer: string;
  correctAnswer: string;
  isCorrect: boolean;
  subject: string;
}
