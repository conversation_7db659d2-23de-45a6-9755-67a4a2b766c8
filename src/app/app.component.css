.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.app-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.app-title {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  margin: 0;
}

.app-title i {
  margin-right: 10px;
}

.app-nav {
  display: flex;
  gap: 20px;
}

.nav-link {
  text-decoration: none;
  color: #6c757d;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.nav-link.active {
  color: #667eea;
  background: rgba(102, 126, 234, 0.15);
}

.nav-link i {
  margin-right: 5px;
}

.app-main {
  flex: 1;
  padding: 20px 0;
}

.app-footer {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 20px 0;
  text-align: center;
  color: #6c757d;
  font-size: 14px;
}

@media (max-width: 768px) {
  .app-header .container {
    flex-direction: column;
    gap: 15px;
  }
  
  .app-nav {
    width: 100%;
    justify-content: center;
  }
  
  .nav-link {
    flex: 1;
    text-align: center;
  }
}
