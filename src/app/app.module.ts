import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { TestConfigurationComponent } from './components/test-configuration/test-configuration.component';
import { TestTakingComponent } from './components/test-taking/test-taking.component';
import { TestResultsComponent } from './components/test-results/test-results.component';
import { HomeComponent } from './components/home/<USER>';

@NgModule({
  declarations: [
    AppComponent,
    TestConfigurationComponent,
    TestTakingComponent,
    TestResultsComponent,
    HomeComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    FormsModule,
    ReactiveFormsModule
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
