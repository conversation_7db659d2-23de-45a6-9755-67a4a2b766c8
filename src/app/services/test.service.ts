import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { 
  TestData, 
  TestConfiguration, 
  TestSession, 
  QuestionWithMetadata, 
  TestResult, 
  SubjectResult, 
  QuestionResult,
  Question 
} from '../models/question.model';
import { testData } from '../data/test-data';

@Injectable({
  providedIn: 'root'
})
export class TestService {
  private currentSessionSubject = new BehaviorSubject<TestSession | null>(null);
  public currentSession$ = this.currentSessionSubject.asObservable();

  private testResultSubject = new BehaviorSubject<TestResult | null>(null);
  public testResult$ = this.testResultSubject.asObservable();

  constructor() { }

  getAvailableSubjects(): string[] {
    return Object.keys(testData);
  }

  getSubjectQuestionCount(subject: string): number {
    return testData[subject] ? testData[subject].length : 0;
  }

  createTestSession(configuration: TestConfiguration): TestSession {
    const questions = this.generateQuestions(configuration);
    
    const session: TestSession = {
      configuration,
      questions,
      currentQuestionIndex: 0,
      answers: {},
      startTime: new Date(),
      isCompleted: false
    };

    this.currentSessionSubject.next(session);
    return session;
  }

  private generateQuestions(config: TestConfiguration): QuestionWithMetadata[] {
    const allQuestions: QuestionWithMetadata[] = [];

    // Збираємо питання з обраних предметів
    config.selectedSubjects.forEach(subject => {
      const subjectQuestions = testData[subject] || [];
      const questionsToTake = Math.min(
        config.questionsPerSubject[subject] || 0,
        subjectQuestions.length
      );

      // Перемішуємо питання якщо потрібно
      let questions = [...subjectQuestions];
      if (config.shuffleQuestions) {
        questions = this.shuffleArray(questions);
      }

      // Беремо потрібну кількість питань
      const selectedQuestions = questions.slice(0, questionsToTake);

      // Додаємо метадані
      selectedQuestions.forEach((question, index) => {
        const questionWithMetadata: QuestionWithMetadata = {
          ...question,
          subject,
          originalIndex: index,
          shuffledOptions: config.shuffleOptions ? 
            this.shuffleArray([...question.options]) : 
            question.options
        };
        allQuestions.push(questionWithMetadata);
      });
    });

    // Перемішуємо всі питання якщо потрібно
    if (config.shuffleQuestions) {
      return this.shuffleArray(allQuestions);
    }

    return allQuestions;
  }

  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  getCurrentSession(): TestSession | null {
    return this.currentSessionSubject.value;
  }

  updateCurrentSession(session: TestSession): void {
    this.currentSessionSubject.next(session);
  }

  answerQuestion(questionIndex: number, answer: string): void {
    const session = this.getCurrentSession();
    if (session) {
      session.answers[questionIndex] = answer;
      this.updateCurrentSession(session);
    }
  }

  nextQuestion(): boolean {
    const session = this.getCurrentSession();
    if (session && session.currentQuestionIndex < session.questions.length - 1) {
      session.currentQuestionIndex++;
      this.updateCurrentSession(session);
      return true;
    }
    return false;
  }

  previousQuestion(): boolean {
    const session = this.getCurrentSession();
    if (session && session.currentQuestionIndex > 0) {
      session.currentQuestionIndex--;
      this.updateCurrentSession(session);
      return true;
    }
    return false;
  }

  goToQuestion(index: number): boolean {
    const session = this.getCurrentSession();
    if (session && index >= 0 && index < session.questions.length) {
      session.currentQuestionIndex = index;
      this.updateCurrentSession(session);
      return true;
    }
    return false;
  }

  completeTest(): TestResult {
    const session = this.getCurrentSession();
    if (!session) {
      throw new Error('No active test session');
    }

    session.endTime = new Date();
    session.isCompleted = true;
    this.updateCurrentSession(session);

    const result = this.calculateResults(session);
    this.testResultSubject.next(result);
    
    return result;
  }

  private calculateResults(session: TestSession): TestResult {
    const questionResults: QuestionResult[] = [];
    const subjectStats: { [subject: string]: { correct: number; total: number; questions: QuestionResult[] } } = {};

    let correctAnswers = 0;

    session.questions.forEach((question, index) => {
      const userAnswer = session.answers[index] || '';
      const isCorrect = userAnswer === question.correct;
      
      if (isCorrect) {
        correctAnswers++;
      }

      const questionResult: QuestionResult = {
        question,
        userAnswer,
        correctAnswer: question.correct,
        isCorrect,
        subject: question.subject
      };

      questionResults.push(questionResult);

      // Статистика по предметах
      if (!subjectStats[question.subject]) {
        subjectStats[question.subject] = { correct: 0, total: 0, questions: [] };
      }
      
      subjectStats[question.subject].total++;
      if (isCorrect) {
        subjectStats[question.subject].correct++;
      }
      subjectStats[question.subject].questions.push(questionResult);
    });

    const subjectResults: SubjectResult[] = Object.keys(subjectStats).map(subject => ({
      subject,
      correct: subjectStats[subject].correct,
      total: subjectStats[subject].total,
      percentage: Math.round((subjectStats[subject].correct / subjectStats[subject].total) * 100),
      questions: subjectStats[subject].questions
    }));

    const totalQuestions = session.questions.length;
    const percentage = Math.round((correctAnswers / totalQuestions) * 100);
    const timeSpent = session.endTime ? 
      Math.round((session.endTime.getTime() - session.startTime.getTime()) / 1000) : 0;

    return {
      session,
      score: correctAnswers,
      percentage,
      correctAnswers,
      totalQuestions,
      timeSpent,
      subjectResults
    };
  }

  getTestResult(): TestResult | null {
    return this.testResultSubject.value;
  }

  resetTest(): void {
    this.currentSessionSubject.next(null);
    this.testResultSubject.next(null);
  }

  getProgress(): number {
    const session = this.getCurrentSession();
    if (!session || session.questions.length === 0) {
      return 0;
    }
    return Math.round(((session.currentQuestionIndex + 1) / session.questions.length) * 100);
  }

  getAnsweredQuestionsCount(): number {
    const session = this.getCurrentSession();
    if (!session) {
      return 0;
    }
    return Object.keys(session.answers).length;
  }

  isQuestionAnswered(index: number): boolean {
    const session = this.getCurrentSession();
    return session ? session.answers.hasOwnProperty(index) : false;
  }

  formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}
