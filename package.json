{"name": "testing-system", "version": "1.0.0", "description": "Interactive Testing System", "scripts": {"ng": "ng", "start": "node server.js", "build": "webpack --mode development", "build:prod": "webpack --mode production", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "dependencies": {"@angular/animations": "^8.2.14", "@angular/common": "^8.2.14", "@angular/compiler": "^8.2.14", "@angular/core": "^8.2.14", "@angular/forms": "^8.2.14", "@angular/platform-browser": "^8.2.14", "@angular/platform-browser-dynamic": "^8.2.14", "@angular/router": "^8.2.14", "express": "^4.17.1", "rxjs": "~6.4.0", "tslib": "^1.10.0", "zone.js": "~0.9.1"}, "devDependencies": {"@angular-devkit/build-angular": "~0.803.29", "@angular/cli": "~8.3.29", "@angular/compiler-cli": "^8.2.14", "@angular/language-service": "^8.2.14", "@types/jasmine": "~3.3.8", "@types/jasminewd2": "~2.0.3", "@types/node": "~8.9.4", "codelyzer": "^5.0.0", "css-loader": "^7.1.2", "html-loader": "^5.1.0", "html-webpack-plugin": "^5.6.4", "jasmine-core": "~3.4.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~4.1.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "~2.0.1", "karma-jasmine": "~2.0.1", "karma-jasmine-html-reporter": "^1.4.0", "protractor": "~5.4.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "ts-node": "~7.0.0", "tslint": "~5.15.0", "typescript": "~3.5.3", "webpack": "^5.101.3", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}}